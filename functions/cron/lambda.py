
import pg8000
import json
import os
from libs.aws.aws_session import AwsSession
from libs.functions.dict import create_dict


my_session = AwsSession()

DB_CREDENTIALS = os.environ.get(
    'DB_CREDENTIALS',
    '{"user": "postgres", "pass": "dev-amm-atenea-cluster", "host": "dev-amm-atenea-cluster.cluster-cf995cnswube.us-east-1.rds.amazonaws.com", "db": "ammdwh", "port": 5432}'
)

BUCKET = os.environ.get('BUCKET_NAME', 'ammper-rea-dev')
STAGE = os.environ.get('STAGE', 'local')
ARN_STEP_FUNCTION = os.environ.get(
    'ARN_STEP_FUNCTION',
    'arn:aws:states:us-east-2:174380300553:stateMachine:ReaDevStepFunction'
)
PATH_DOWNLOAD_FILES = '/tmp/download/'


def connect_db_func():
    DB_CREDENTIALS_DATA = json.loads(DB_CREDENTIALS)
    connection_db = pg8000.connect(
        host=DB_CREDENTIALS_DATA['host'],
        user=DB_CREDENTIALS_DATA['user'],
        password=DB_CREDENTIALS_DATA['pass'],
        port=DB_CREDENTIALS_DATA['port'],
        database=DB_CREDENTIALS_DATA['db']
    )
    return connection_db


def get_participants():
    sql = f"""
        select distinct(tcp.participante),
            tcc.sistema_cenace as sistema
        from
            ti_cat_participantes tcp
        join ti_cat_clientes tcc on
            tcc.asset_id = tcp.asset_id
        where
            tcp.participante != 'G118'
            and tcp.participante != 'M028'
            and tcp.participante != 'G079'
    """
    db_cursor.execute(sql)
    participants = db_cursor.fetchall()
    columns = [desc[0] for desc in db_cursor.description]
    participants = [create_dict(columns, row) for row in participants]
    print("PARTICIPANTS", participants)
    return participants


def execute_step_function(participants, test_items):
    step_function_client = my_session.init_aws_service(
        'stepfunctions', 'client')
    print(ARN_STEP_FUNCTION)
    step_function_client.start_execution(
        stateMachineArn=ARN_STEP_FUNCTION,
        input=json.dumps({
            "Processes": participants,
            "TestItems": test_items
        })
    )


def handler(event=None, context=None):
    global db_cursor
    test_items = event.get('test_events', [])
    if len(test_items) > 0:
        execute_step_function(None, test_items)
        return {
            'statusCode': 200,
            'data': "Procesos ejecutados exitosamente"
        }
    db_connection = connect_db_func()
    db_connection.autocommit = True
    db_cursor = db_connection.cursor()
    try:
        participants = get_participants()
        execute_step_function(participants, None)
    except Exception as E:
        raise Exception(json.dumps({
            'statusCode': 500,
            'body': {
                'stataus': 'ERROR',
                'description': f'Error on scraper: {E}',
                'data': "message_data"
            }
        }))
    finally:
        db_connection.close()

    return {
        'statusCode': 200,
        'data': "Procesos ejecutados exitosamente"
    }


if __name__ == '__main__':
    # handler({'data': {'participante': 'C006', 'monto_garantizado': 310700000.0, 'pas_conocido': -59595635.51,
    #         'pas_potencial': 174358008.21, 'rea': 114762372.7, 'porcentaje': 36.9367, 'estatus': 'verde'}})
    pass

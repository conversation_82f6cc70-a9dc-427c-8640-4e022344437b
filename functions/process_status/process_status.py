

def handler(event, ctx):
    errors = []
    for i in event:
        if i.get('statusCode', 500) == 500 and i.get('status', 'ERROR') == 'ERROR':
            errors.append({
                'cliente': f"{i['participante']} {i['sistema']}",
                'participante': i['participante'],
                'sistema_cenace': i['sistema'],
                'fecha': i['fecha']
            })
    if len(errors) > 0:
        return {
            "statusCode": 500,
            "status": "ERROR",
            "type": "PROCESS_LIST",
            "items": errors
        }

    return {
        "statusCode": 200,
        "status": "SUCCESS"
    }

# comment comment
service: ps-op-proceso-rea

provider:
  name: aws
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.config.${self:custom.stage}.region}

  deploymentBucket:
    name: serverless-deployment-${self:custom.config.${self:custom.stage}.account_id}-global-bucket-${self:custom.stage}
    serverSideEncryption: AES256

  versionFunctions: false

  environment:
    REGION: ${self:custom.config.${self:custom.stage}.region}
    STAGE: ${self:custom.stage}
    BUCKET_NAME: ${self:custom.config.${self:custom.stage}.name_bucket}
    DB_CREDENTIALS: ${ssm:/db/dwh/${self:custom.config.${self:custom.stage}.second_format_stage}/mrodrigo}
    RULES_REA: ${ssm:/rea/rules/${self:custom.config.${self:custom.stage}.second_format_stage}/mrodrigo}
    ARN_STEP_FUNCTION: arn:aws:states:${self:custom.config.${self:custom.stage}.region}:${self:custom.config.${self:custom.stage}.account_id}:stateMachine:${self:custom.projectName}V2${self:custom.config.${self:custom.stage}.name_stage}StepFunction

  ecr:
    images:
      ps-op-proceso-rea:
        path: scrapper
        platform: linux/amd64

custom:
  stage: ${opt:stage, 'dev'}
  projectName: ${opt:projectName, 'Rea'}
  config:
    dev:
      region: ${opt:region, 'us-east-2'}
      account_id: '************'
      name_stage: 'Dev'
      second_format_stage: 'dev'
      name_bucket: 'ammper-rea-dev'
    prd:
      region: ${opt:region, 'us-east-2'}
      account_id: '************'
      name_stage: 'Prod'
      second_format_stage: 'prod'
      name_bucket: 'ammper-rea-prod'
  pythonRequirements:
    useDownloadCache: false
    useStaticCache: true
    cacheLocation: '../cache_python_requirements_plugin'
    dockerizePip: false

package:
  individually: true
  exclude:
    - node_modules/**
    - .git/**
    - package-lock.json

functions:
  scrapper:
    timeout: 900
    role: arn:aws:iam::************:role/ps-op-proceso-rea-${self:custom.stage}-Role
    image:
      name: ps-op-proceso-rea
    tags:
      author: caguilar
      env: ${self:custom.stage}
      project-name: proceso-rea
    

  process_status:
    module: process_status
    handler: process_status.handler
    role: arn:aws:iam::************:role/ps-op-proceso-rea-${self:custom.stage}-Role
    tags:
      author: caguilar
      env: ${self:custom.stage}
      project-name: proceso-rea
    package:
      patterns:
        - process_status/**

  process_run:
    module: process_init
    handler: process_run.handler
    role: arn:aws:iam::************:role/ps-op-proceso-rea-${self:custom.stage}-Role
    tags:
      author: caguilar
      env: ${self:custom.stage}
      project-name: proceso-rea
    package:
      patterns:
        - process_init/**

  process_error:
    module: process_error
    handler: lambda.handler
    role: arn:aws:iam::************:role/ps-op-proceso-rea-${self:custom.stage}-Role
    tags:
      author: caguilar
      env: ${self:custom.stage}
      project-name: proceso-rea
    package:
      patterns:
        - process_error/**
  
  process_data:
    module: process_data
    handler: lambda.handler
    role: arn:aws:iam::************:role/ps-op-proceso-rea-${self:custom.stage}-Role
    tags:
      author: caguilar
      env: ${self:custom.stage}
      project-name: proceso-rea
    package:
      patterns:
        - process_data/**

  notificaton:
    module: notification
    handler: lambda.handler
    role: arn:aws:iam::************:role/ps-op-proceso-rea-${self:custom.stage}-Role
    tags:
      author: caguilar
      env: ${self:custom.stage}
      project-name: proceso-rea
    package:
      patterns:
        - notification/**
  
  gateway:
    module: gateway
    handler: lambda.handler
    role: arn:aws:iam::************:role/ps-op-proceso-rea-${self:custom.stage}-Role
    tags:
      author: caguilar
      env: ${self:custom.stage}
      project-name: proceso-rea
    package:
      patterns:
        - gateway/**
    events:
      - http:
          method: POST
          path: /find_reas
          private: false
          cors: true
  
  cron2:
    module: cron2
    handler: lambda.handler
    role: arn:aws:iam::************:role/ps-op-proceso-rea-${self:custom.stage}-Role
    tags:
      author: caguilar
      env: ${self:custom.stage}
      project-name: proceso-rea
    package:
      patterns:
        - cron2/**
  
  cron:
    module: cron
    handler: lambda.handler
    role: arn:aws:iam::************:role/ps-op-proceso-rea-${self:custom.stage}-Role
    tags:
      author: caguilar
      env: ${self:custom.stage}
      project-name: proceso-rea
    package:
      patterns:
        - cron/**

plugins:
  - serverless-python-requirements
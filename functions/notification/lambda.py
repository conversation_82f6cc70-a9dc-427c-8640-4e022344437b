import pg8000
import json
import os
import uuid
import boto3
from datetime import datetime, time, timezone
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.image import MIMEImage
from email.mime.text import MIMEText
from botocore.exceptions import ClientError

from pybars import Compiler

DB_CREDENTIALS = os.environ.get(
    'DB_CREDENTIALS',
    '{"user": "mshabes", "pass": "8Q98o05MmDJpt2su", "host": "amm-dwh.cf995cnswube.us-east-1.rds.amazonaws.com", "db": "ammdwh", "port": 5432}'
)
RULES_REA = os.environ.get(
    'RULES_REA', '{"rules":{"limit_ranges":{"personal":{"min":70,"max":80},"alerta_1":{"min":80,"max":90},"alerta_2":{"min":90,"max":100},"alerta_3":{"min":100,"max":************}}}}'
)
REGION = os.getenv('REGION', 'us-east-1')
STAGE = os.getenv('STAGE', 'dev')

if STAGE == "prd":
    STAGE == "prod"

client = boto3.client('secretsmanager', region_name=REGION)

compiler = Compiler()

range_rules = json.loads(RULES_REA)
limit_ranges = range_rules['rules']['limit_ranges']


def connect_db_func():
    DB_CREDENTIALS_DATA = json.loads(DB_CREDENTIALS)
    connection_db = pg8000.connect(
        host=DB_CREDENTIALS_DATA['host'],
        user=DB_CREDENTIALS_DATA['user'],
        password=DB_CREDENTIALS_DATA['pass'],
        port=DB_CREDENTIALS_DATA['port'],
        database=DB_CREDENTIALS_DATA['db']
    )
    return connection_db


def upload_error_scraper_to_db(data):
    sql = "INSERT INTO public.ti_rea(participante, sistema_cenace, has_error_getdata) VALUES (%s, %s, %s) RETURNING id_rea"
    result = db_cursor.execute(
        sql,
        (
            data['participante'],
            data['sistema'],
            True
        )
    )
    result = db_cursor.fetchone()[0]
    print("UPLOADED TO DB")
    return result


def get_rea_by_id(id_rea):
    sql = "SELECT id_rea, fecha, participante, sistema_cenace, mto_garantizado, pas_conocido, pas_potencial, rea, porcentaje, estatus, razon_social FROM public.ti_rea WHERE id_rea = %s"
    result = db_cursor.execute(
        sql,
        (
            id_rea,
        )
    )
    result = db_cursor.fetchone()
    if len(result) > 0:
        return {
            'id_rea': result[0],
            'fecha': result[1],
            'razon_social': result[10],
            'participante': result[2],
            'sistema': result[3],
            'monto_garantizado': result[4],
            'pas_conocido': result[5],
            'pas_potencial': result[6],
            'rea': result[7],
            'porcentaje': result[8],
            'estatus': result[9]
        }
    else:
        raise Exception('Error al obtener informacion')


def get_secret_value(secret_name=f'/rea/emails/prod/mrodrigo'):
    try:
        get_secret_value_response = client.get_secret_value(
            SecretId=secret_name
        )
    except ClientError as e:
        raise e
    values = json.loads(get_secret_value_response['SecretString'])
    return values


def send_email(participante, message_email, razon_social, isError=False):

    # client_ses = boto3.client("ses", region_name="us-east-1")
    sts = boto3.client('sts')

    stsresponse = sts.assume_role(
        RoleArn='arn:aws:iam::************:role/CrossAccountSESSendEmail',
        RoleSessionName='CrossAccountsecretsmanager'
    )

    # Save the details from assumed role into vars
    newsession_access_key = stsresponse["Credentials"]["AccessKeyId"]
    newsession_secret_access_key = stsresponse["Credentials"]["SecretAccessKey"]
    newsession_session_token = stsresponse["Credentials"]["SessionToken"]

    client_ses = boto3.client(
        "ses",
        aws_access_key_id=newsession_access_key,
        aws_secret_access_key=newsession_secret_access_key,
        aws_session_token=newsession_session_token,
        region_name=REGION
    )

    CHARSET = "UTF-8"

    with open('logo.png', 'rb') as f:
        img_data = f.read()

    img = MIMEImage(img_data, name=os.path.basename('logo.png'))
    img.add_header('Content-ID', '<logo>')

    msg = MIMEMultipart()
    if isError:
        emails_error = get_secret_value()
        msg['From'] = emails_error['from']
        msg['To'] = ", ".join(json.loads(emails_error['error']))
    else:
        emails = get_secret_value()
        msg['From'] = emails['from']
        if participante in emails:
            msg['To'] = ", ".join(json.loads(emails[participante]))
            msg['Bcc'] = ", ".join(json.loads(emails['default']))
        else:
            msg['To'] = ", ".join(json.loads(emails['default']))

    msg['Subject'] = f"REA {razon_social}"
    msg.attach(MIMEText(message_email, 'html'))
    msg.attach(img)
    response = client_ses.send_raw_email(
        # Source=msg['From'],
        # Destinations=msg['To'],
        RawMessage={'Data': msg.as_string()}
    )
    print("-----------------------------")
    print(response)
    # print(msg['From'])
    # print(msg['To'])
    # print(msg['Bcc'])
    print("SUCCESS")


def build_html(body):

    f = open("index.html", "r")
    source = f.read()
    template = compiler.compile(source)

    colors_html = {
        'rojo': {
            'color': '#fffefe',
            'background': '#ff0000'
        },
        'verde': {
            'color': '#373737',
            'background': '#00ff1e'
        },
        'amarillo': {
            'color': '#373737',
            'background': '#f2ff00'
        },
        'ambar': {
            'color': '#373737',
            'background': '#ff7700'
        },
        'rojo (incumplimiento)': {
            'color': '#fffefe',
            'background': '#ff0000'
        }
    }

    now_dt = datetime.now()

    output = template({
        'client_name': body['razon_social'] if body['razon_social'] is not None else f"{body['participante']} - {body['sistema']}",
        'participante': body['participante'],
        'sistema_cenace': body['sistema'],
        'mto_garantizado': format(body['monto_garantizado'], ",.5f"),
        'pas_conocido': format(body['pas_conocido'], ",.5f"),
        'pas_potencial': format(body['pas_potencial'], ",.5f"),
        'rea': format(body['rea'], ",.5f"),
        'porcentaje': round(body['porcentaje'], 2),
        'estatus': body['estatus'].upper(),
        'fecha': body['fecha'].strftime("%Y-%m-%d %H:%M:%S"),
        'style_color': colors_html[body['estatus']]['color'],
        'style_background': colors_html[body['estatus']]['background'],
        'has_error': body['has_error'],
        'text_incumplimiento': '🚨🚨🚨' if body['estatus'] == 'rojo (incumplimiento)' else '',
        'year_cp': now_dt.year
    })

    return output


def build_html_error(body):

    f = open("index_errors.html", "r")
    source = f.read()
    template = compiler.compile(source)

    now_dt = datetime.now()

    body['year_cp'] = now_dt.year
    output = template(body)

    return output


def evaluate_alert_type(porcentaje):
    if porcentaje >= limit_ranges['personal']['min'] and porcentaje < limit_ranges['personal']['max']:
        return {**limit_ranges['personal'], 'limit_type': 0}
    elif porcentaje >= limit_ranges['alerta_1']['min'] and porcentaje < limit_ranges['alerta_1']['max']:
        return {**limit_ranges['alerta_1'], 'limit_type': 1}
    elif porcentaje >= limit_ranges['alerta_2']['min'] and porcentaje < limit_ranges['alerta_2']['max']:
        return {**limit_ranges['alerta_2'], 'limit_type': 2}
    elif porcentaje >= limit_ranges['alerta_3']['min'] and porcentaje < limit_ranges['alerta_3']['max']:
        return {**limit_ranges['alerta_3'], 'limit_type': 3}
    else:
        return None


def get_notifications(participante, sistema_cenace, tipo_alerta):

    def start_of_day(dt: datetime) -> datetime:
        return datetime.combine(dt, time.min, tzinfo=dt.tzinfo)

    def end_of_day(dt: datetime) -> datetime:
        return datetime.combine(dt, time.max, tzinfo=dt.tzinfo)

    def now_utc() -> datetime:
        return datetime.now(tz=timezone.utc)

    start_of_day = start_of_day(now_utc())
    end_of_day = end_of_day(now_utc())

    sql = f"select * from public.ti_rea_notification where fecha between '{start_of_day}' and '{end_of_day}' and participante='{participante}' and sistema_cenace='{sistema_cenace}' and tipo_alerta>={tipo_alerta} ORDER BY fecha DESC;"
    print('GET NOTIFICATIONS', sql)

    db_cursor.execute(sql)

    def rows_as_dicts(cursor):
        col_names = [i[0] for i in cursor.description]
        for row in cursor:
            x = dict(zip(col_names, row))
            yield x

    rows = rows_as_dicts(db_cursor)
    items = []
    for row in rows:
        items.append(row)
    return items


def upload_notification_to_db(data):
    sql = "INSERT INTO public.ti_rea_notification(id, participante, sistema_cenace, fecha, porcentaje, tipo_alerta, umbral_minimo, umbral_maximo) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)"
    db_cursor.execute(
        sql,
        (
            str(uuid.uuid4()),
            data['participante'],
            data['sistema_cenace'],
            datetime.now(),
            data['porcentaje'],
            data['tipo_alerta'],
            data['umbral_minimo'],
            data['umbral_maximo']
        )
    )


def handler(event=None, context=None):
    global db_cursor
    db_connection = connect_db_func()
    db_connection.autocommit = True
    db_cursor = db_connection.cursor()

    if event.get('status') == 'ERROR' and event.get('type') == 'PROCESS_LIST':
        result = {
            'has_error': True,
            'data': event.get('items')
        }

        html = build_html_error(result)
        send_email('error',
                   html, f"ERROR EN EL PROCESO", True)

    elif event.get('status') == 'ERROR' and event.get('type') != 'PROCESS_LIST':
        rea_id = upload_error_scraper_to_db(event)
        result = get_rea_by_id(rea_id)
        result['estatus'] = 'rojo'
        result['has_error'] = True

        event['rea_id'] = rea_id
        event['fecha'] = result.get('fecha').strftime('%Y-%m-%d %H:%M:%S')
    else:
        result = get_rea_by_id(event['data']['rea_id'])
        result['has_error'] = False
        current_evaluation = evaluate_alert_type(result['porcentaje'])
        if current_evaluation:
            current_notificacions = get_notifications(
                event['data_client']['participante'],
                event['data_client']['sistema'],
                current_evaluation['limit_type']
            )
            current_notificacions = []
            if len(current_notificacions) == 0:
                upload_notification_to_db({
                    'participante': event['data_client']['participante'],
                    'sistema_cenace': event['data_client']['sistema'],
                    'porcentaje': result['porcentaje'],
                    'tipo_alerta': current_evaluation['limit_type'],
                    'umbral_minimo': current_evaluation['min'],
                    'umbral_maximo': current_evaluation['max']
                })
                html = build_html(result)
                send_email(event['data']['participante'],
                           html, result['razon_social'])

    return event


if __name__ == '__main__':
    # payload = {
    #     "statusCode": 500,
    #     "status": "ERROR",
    #     "type": "PROCESS_LIST",
    #     "items": [
    #         {
    #             "cliente": "C006 SIN",
    #             "participante": "C006",
    #             "sistema_cenace": "SIN",
    #             "fecha": "2024-06-03 08:01:13"
    #         },
    #         {
    #             "cliente": "G083 SIN",
    #             "participante": "G083",
    #             "sistema_cenace": "SIN",
    #             "fecha": "2024-06-03 08:02:58"
    #         }
    #     ]
    # }
    # handler(payload, None)
    pass

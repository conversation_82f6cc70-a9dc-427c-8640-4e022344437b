import json
import os
import pg8000


headers = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "*",
    "Access-Control-Allow-Methods": "POST",
} 

DB_CREDENTIALS = os.environ.get(
    'DB_CREDENTIALS', '{"user": "postgres", "pass": "dev-amm-atenea-cluster", "host": "dev-amm-atenea-cluster.cluster-cf995cnswube.us-east-1.rds.amazonaws.com", "db": "ammdwh", "port": 5432}')


class ValidationPropertyException(Exception):
    """Exception raised for errors in the input .

    Attributes:
        errors -- input  which caused the error
        message -- explanation of the error
    """

    def __init__(self, errors):
        self.errors = {
            "status": 422,
            "errors": errors
        }
        self.status_code = 422
        super().__init__(errors)


def connect_db_func():
    DB_CREDENTIALS_DATA = json.loads(DB_CREDENTIALS)
    connection_db = pg8000.connect(
        host=DB_CREDENTIALS_DATA['host'],
        user=DB_CREDENTIALS_DATA['user'],
        password=DB_CREDENTIALS_DATA['pass'],
        port=DB_CREDENTIALS_DATA['port'],
        database=DB_CREDENTIALS_DATA['db']
    )
    return connection_db


def select_from_db(start_date, end_date, participante, sistema_cenace):

    initial_sql = f"select * from public.ti_rea where fecha between '{start_date}' and '{end_date}'"

    if participante:
        initial_sql += f" and participante='{participante}' "
    if sistema_cenace:
        initial_sql += f" and sistema_cenace='{sistema_cenace}' "

    sql = initial_sql + " ORDER BY fecha;"

    print('SELECT SQL: ', sql)
    db_cursor.execute(sql)

    def rows_as_dicts(cursor):
        col_names = [i[0] for i in cursor.description]
        for row in cursor:
            x = dict(zip(col_names, row))
            yield x

    rows = rows_as_dicts(db_cursor)
    items = []
    for row in rows:
        row['error_message'] = "Se presentó un error al obtener la data." if row['has_error_getdata'] else "Se presentó un error al guardar la data." if row["has_error_processdata"] else None
        items.append({
            key: value for key, value in row.items() if 'send_' not in key
        })
    return items


def handler(event, ctx):
    headers = {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*"
    }

    try:
        body = json.loads(event["body"])
        # body = event

        global db_cursor, data, data_client
        db_connection = connect_db_func()
        db_connection.autocommit = True
        db_cursor = db_connection.cursor()

        if ("fecha_inicio" not in body):
            raise ValidationPropertyException("fecha_inicio es requerido")

        if ("fecha_fin" not in body):
            raise ValidationPropertyException("fecha_fin es requerido")

        fecha_inicio = body['fecha_inicio']
        fecha_fin = body['fecha_fin']
        participante = body.get('participante', None)
        sistema_cenace = body.get('sistema', None)

        response = select_from_db(fecha_inicio, fecha_fin,
                                  participante, sistema_cenace)

        return {
            "statusCode": 200,
            "body": json.dumps({
                "status": 200,
                "items": response
            }, default=str),
            "headers": headers,
            "isBase64Encoded": False
        }
    except ValidationPropertyException as ex:
        return {
            "statusCode": ex.status_code,
            "body": json.dumps(ex.errors),
            "headers": headers,
            "isBase64Encoded": False
        }
    except Exception as e:
        return {
            "statusCode": 500,
            "body": json.dumps({
                "message": str(e)
            }, default=str),
            "headers": headers,
            "isBase64Encoded": False
        }


if __name__ == '__main__':
    payload = {
        "fecha_inicio": "2023-12-01",
        "fecha_fin": "2023-12-02",
        "participante": "C006",
        "sistema": "BCS"
    }
    # response = handler(payload, None)
    # print(response)

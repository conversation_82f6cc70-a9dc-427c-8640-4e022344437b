from time import sleep


def handler(event=None, context=None):

    if 'data_client' in event and 'numberIntents' in event['data_client'] and event['data_client']['numberIntents'] < 3:
        event['data_client']['numberIntents'] += 1
        event['data_client']['hasReScrapper'] = 1
    else:
        event['data_client']['hasReScrapper'] = 0

    event['data_client']['status'] = event["status"]
    event['data_client']['statusCode'] = event["statusCode"]
    event['data_client']['hasScrapper'] = event["hasScrapper"]
    # sleep(10)
    return event['data_client']


if __name__ == '__main__':
    pass

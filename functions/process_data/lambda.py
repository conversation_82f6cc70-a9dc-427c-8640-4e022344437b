from datetime import datetime
import pg8000
import pytz
import json
import csv
import os

from libs.aws.aws_session import S3


my_session_s3 = S3()

DB_CREDENTIALS = os.environ.get(
    'DB_CREDENTIALS',
    '{"user": "postgres", "pass": "dev-amm-atenea-cluster", "host": "dev-amm-atenea-cluster.cluster-cf995cnswube.us-east-1.rds.amazonaws.com", "db": "ammdwh", "port": 5432}'
)
BUCKET = "ammper-rea-prod"
STAGE = os.environ.get('STAGE', 'local')
PATH_DOWNLOAD_FILES = '/tmp/download/'


def connect_db_func():
    DB_CREDENTIALS_DATA = json.loads(DB_CREDENTIALS)
    connection_db = pg8000.connect(
        host=DB_CREDENTIALS_DATA['host'],
        user=DB_CREDENTIALS_DATA['user'],
        password=DB_CREDENTIALS_DATA['pass'],
        port=DB_CREDENTIALS_DATA['port'],
        database=DB_CREDENTIALS_DATA['db']
    )
    return connection_db


def upload_to_db():
    sql = "INSERT INTO public.ti_rea(participante, sistema_cenace, mto_garantizado, pas_conocido, pas_potencial, rea, porcentaje, estatus, razon_social) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s) RETURNING id_rea"
    result = db_cursor.execute(
        sql,
        (
            data['participante'],
            data_client['sistema'],
            data['monto_garantizado'],
            data['pas_conocido'],
            data['pas_potencial'],
            data['rea'],
            data['porcentaje'],
            data['estatus'],
            data['razon_social']
        )
    )
    result = db_cursor.fetchone()[0]
    print("UPLOADED TO DB")
    return result


def create_csv_file():
    os.makedirs(PATH_DOWNLOAD_FILES, exist_ok=True)
    Tz = pytz.timezone('America/Mexico_City')
    date_now = datetime.now(Tz).strftime("%d_%m_%Y %H_%M")
    name_file = f"rea_{date_now}.csv"
    path_file = PATH_DOWNLOAD_FILES + data["participante"] + "_" + name_file

    headers = list(data.keys())
    with open(path_file, "w") as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        writer.writerows([data])

    destination_s3 = f"032/raw/{data['participante']}/{name_file}"
    my_session_s3.upload_file(BUCKET, path_file, destination_s3)
    print(f"UPLOADED FILE {destination_s3} TO BUCKET {BUCKET}")


def handler(event=None, context=None):
    global db_cursor, data, data_client
    print("EVENT", event, "CONTEXT", context)
    db_connection = connect_db_func()
    db_connection.autocommit = True
    db_cursor = db_connection.cursor()
    data = event["data"]
    data_client = event["data_client"]
    try:
        rea_id = upload_to_db()
        data['rea_id'] = rea_id
        create_csv_file()
    except Exception as E:
        print(str(E))
        return {
            'statusCode': 500,
            'status': 'ERROR',
            'hasProcessData': True,
            'data': data,
            'data_client': data_client,
        }
    finally:
        db_connection.close()

    return {
        'statusCode': 200,
        'hasProcessData': True,
        'status': 'SUCCESS',
        'data': data,
        'data_client': data_client,
        'messager': "SUBIDO EXITOSAMENTE"
    }


if __name__ == '__main__':
    # handler({
    #     "statusCode": 200,
    #     "data": {
    #         'razon_social': 'AMMPER ENERGIA, SAPI DE CV',
    #         'participante': 'C006',
    #         'monto_garantizado': 320700000.0,
    #         'pas_conocido': -4416466.26,
    #         'pas_potencial': 163928597.52,
    #         'rea': 159512131.26,
    #         'porcentaje': 49.7387,
    #         'estatus': 'verde'
    #     },
    #     "data_client": {
    #         "participante": "C006",
    #         "sistema": "BCS"
    #     }
    # })
    pass

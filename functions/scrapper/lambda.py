import os
import re
import time
import base64
from tempfile import mkdtemp
from selenium import webdriver
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import NoSuchElementException, ElementNotInteractableException
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import json
import deprecation

from libs.aws.aws_session import AwsSession

my_session = AwsSession()

BUCKET = os.environ.get('BUCKET_NAME', 'amm-clients-documents-dev')
BUCKET_WORK = os.environ.get('BUCKET_WORK_NAME', 'amm-clients-documents-work')
STAGE = os.environ.get('STAGE', 'local')
PATH_DOWNLOAD_FILES = '/tmp/download/'


def get_credentials_from_pci_account():
    session_cross_account = my_session.cross_account_connection_service(
        "arn:aws:iam::************:role/CrossAccountSecretsManagerPCI", "secretsmanager", "client")
    results = session_cross_account.list_secrets()
    print(results)
    secret_Id = f'/prod/CenaceAccess/{message_data["participante"]}/{message_data["sistema"]}'
    print(secret_Id)
    secret = session_cross_account.get_secret_value(
        SecretId=secret_Id)
    return json.loads(secret['SecretString'])


@deprecation.deprecated(details="Use 'get_info_from_table' instead")
def create_dict_from_table():
    def format_data(value):
        cleaned_string = re.sub(r'[^\d.-]', '', value)
        try:
            result = float(cleaned_string)
            return result
        except:
            return value

    razon_social = driver.find_element(
        By.XPATH, "/html/body/div[3]/form/div[1]/div/div[1]/h3/strong").text
    participante = driver.find_element(
        By.XPATH, "/html/body/div[3]/form/div[1]/div/div[2]/div[2]/table/tbody/tr/td[1]/b").text
    monto_garantizado = format_data(driver.find_element(
        By.XPATH, "/html/body/div[3]/form/div[1]/div/div[2]/div[2]/table/tbody/tr/td[2]").text)
    pas_conocido = format_data(driver.find_element(
        By.XPATH, "/html/body/div[3]/form/div[1]/div/div[2]/div[2]/table/tbody/tr/td[3]").text)
    pas_potencial = format_data(driver.find_element(
        By.XPATH, "/html/body/div[3]/form/div[1]/div/div[2]/div[2]/table/tbody/tr/td[4]").text)
    rea = format_data(driver.find_element(
        By.XPATH, "/html/body/div[3]/form/div[1]/div/div[2]/div[2]/table/tbody/tr/td[5]").text)
    porcentaje = format_data(driver.find_element(
        By.XPATH, "/html/body/div[3]/form/div[1]/div/div[2]/div[2]/table/tbody/tr/td[6]").text)
    estatus = driver.find_element(
        By.XPATH, "/html/body/div[3]/form/div[1]/div/div[2]/div[2]/table/tbody/tr/td[7]/div[2]")
    estatus = estatus.get_attribute("class")

    if "Verde" in estatus:
        estatus = "verde"
    elif "Amarillo" in estatus:
        estatus = "amarillo"
    elif "Ambar" in estatus:
        estatus = "ambar"
    elif "Rojo" in estatus:
        estatus = "rojo"
    else:
        estatus = ""

    values = {
        "razon_social": razon_social.split(" - ")[1],
        "participante": participante,
        "monto_garantizado": monto_garantizado,
        "pas_conocido": pas_conocido,
        "pas_potencial": pas_potencial,
        "rea": rea,
        "porcentaje": porcentaje,
        "estatus": estatus
    }

    print("DATOS FORMATEADOS", values)

    return values


def get_info_from_table():
    def format_data(value):
        cleaned_string = re.sub(r'[^\d.-]', '', value)
        try:
            result = float(cleaned_string)
            return result
        except:
            return value

    razon_social = driver.find_element(
        By.XPATH, "/html/body/div[2]/form/div[2]/div/div[1]/h3/strong").text
    participante = driver.find_element(
        By.XPATH, "/html/body/div[2]/form/div[2]/div/div[2]/div[2]/table/tbody/tr/td[1]/b").text
    monto_garantizado = format_data(driver.find_element(
        By.XPATH, "/html/body/div[2]/form/div[2]/div/div[2]/div[2]/table/tbody/tr/td[2]").text)
    pas_conocido = format_data(driver.find_element(
        By.XPATH, "/html/body/div[2]/form/div[2]/div/div[2]/div[2]/table/tbody/tr/td[3]").text)
    pas_potencial = format_data(driver.find_element(
        By.XPATH, "/html/body/div[2]/form/div[2]/div/div[2]/div[2]/table/tbody/tr/td[4]").text)
    rea = format_data(driver.find_element(
        By.XPATH, "/html/body/div[2]/form/div[2]/div/div[2]/div[2]/table/tbody/tr/td[5]").text)
    porcentaje = format_data(driver.find_element(
        By.XPATH, "/html/body/div[2]/form/div[2]/div/div[2]/div[2]/table/tbody/tr/td[6]/b").text)
    estatus = driver.find_element(
        By.XPATH, "/html/body/div[2]/form/div[2]/div/div[2]/div[2]/table/tbody/tr/td[7]/div[2]")
    estatus = estatus.get_attribute("class")

    if "Verde" in estatus:
        estatus = "verde"
    elif "Amarillo" in estatus:
        estatus = "amarillo"
    elif "Ambar" in estatus:
        estatus = "ambar"
    elif "Rojo" in estatus:
        estatus = "rojo"
    else:
        estatus = ""

    values = {
        "razon_social": razon_social.split(" - ")[1],
        "participante": participante,
        "monto_garantizado": monto_garantizado,
        "pas_conocido": pas_conocido,
        "pas_potencial": pas_potencial,
        "rea": rea,
        "porcentaje": porcentaje,
        "estatus": estatus
    }

    print("DATOS FORMATEADOS", values)

    return values


def scraper(credentials):
    global driver, participante, sistema, date_download, id_file, process

    participante = message_data['participante']

    path_cer, path_key, credentials = credentials[
        'path_cer'], credentials['path_key'], credentials['credentials']

    print(path_cer, path_key)

    print("ABRIENDO DRIVER DE CHROME")
    try:
        
        print("Inicializando Chrome")
        options = webdriver.ChromeOptions()
        service = webdriver.ChromeService("/opt/chromedriver")

        options.binary_location = '/opt/chrome/chrome'
        # TODO: Descoment this part
        # options.add_argument("--headless=new")
        options.add_argument('--no-sandbox')
        options.add_argument("--disable-gpu")
        options.add_argument("--start-maximized")
        options.add_argument("--single-process")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-dev-tools")
        options.add_argument("--no-zygote")
        options.add_argument(f"--user-data-dir={mkdtemp()}")
        options.add_argument(f"--data-path={mkdtemp()}")
        options.add_argument(f"--disk-cache-dir={mkdtemp()}")
        options.add_argument("--remote-debugging-port=9222")

        driver = webdriver.Chrome(options=options, service=service)

        # TODO: Change to Production
        driver.get(
            "https://memsim.cenace.gob.mx/Entrenamiento/participantes/LOGIN/Default.aspx"
            )

        # Relogin if the user to login is not the same (CENACE error that logs another user)
        access_to_correct_user = False
        retry = 0
        while access_to_correct_user == False and retry < 3:
            print("INSERTANDO CERTIFICADOS Y CONTRASEÑA")
            # ingresa los certificados
            # input_cer = driver.find_element(By.ID, "uploadCerfile0")
            input_cer = driver.find_element(By.ID, "uploadCer")
            input_cer.send_keys(path_cer)
            time.sleep(2)
            # input_key = driver.find_element(By.ID, "uploadKeyfile0")
            input_key = driver.find_element(By.ID, "uploadKey")
            input_key.send_keys(path_key)
            time.sleep(2)

            input_user = driver.find_element(By.ID, "txtPrivateKey")
            input_user.send_keys(credentials['cert_password'])
            time.sleep(2)

            input_btn = driver.find_element(By.ID, "btnEnviar")
            driver.implicitly_wait(5)
            driver.execute_script("arguments[0].click();", input_btn)

            # Notificacion pronta expiración de certificado
            try:
                driver.find_element(
                    By.XPATH, '//*[@class="RadWindow RadWindow_Default rwNormalWindow rwTransparentWindow"]')

                button_ok = driver.find_element(
                    By.XPATH, "/html/body/form/div[1]/table/tbody/tr[2]/td[2]/div/div/div[2]/a")
                button_ok.click()
            except NoSuchElementException:
                pass

            print("INGRESANDO USUARIO Y CONTRASEÑA")
            # segundo login
            input_log = driver.find_element(By.ID, "txtUsuario")
            input_log.send_keys(credentials['user'])
            time.sleep(2)
            input_pass = driver.find_element(By.ID, "txtPassword")
            # input_pass.send_keys(credentials['password'])
            script = f"arguments[0].value = '{credentials['password']}'"
            driver.execute_script(script, input_pass)
            time.sleep(2)
            try:
                input_btn = driver.find_element(By.ID, "Button1")
                driver.implicitly_wait(5)
                driver.execute_script("arguments[0].click();", input_btn)
            except Exception as B:
                # A veces toma automaticamente el clic al boton de siguiente
                print("")

            # Check if modal notificacion exist
            try:
                modal_element = driver.find_element(
                    By.ID, 'ConfirmDialogPopupExtenderID_foregroundElement')
                if not 'display: none' in modal_element.get_attribute('style'):
                    driver.find_element(By.ID, 'lnkBtnOK').click()
                    time.sleep(10)
                    driver.find_element(By.ID, 'btnConfirmarLectura').click()
                    time.sleep(10)
                    driver.find_element(By.ID, 'lnkBtnAceptar').click()
                    time.sleep(10)
            except Exception as E:
                print('Exception Modal', E)
                raise Exception("Error al cerrar modal de notificacion")

            print("LOGIN REALIZADO CON ÉXITO")
            # Check if user is correct
            try:
                participant_on_sim = driver.find_element(
                    By.ID, 'nombreParticipanteOCentral')
                participant_on_sim = participant_on_sim.text
                if participante not in participant_on_sim:
                    raise Exception("Se ingreso a un usuario incorrecto")
                else:
                    access_to_correct_user = True
            except Exception as E:
                print('Error on check coincidence in participant', E)
                retry += 1
                button_logout = driver.find_element(By.ID, 'btnCerrarSesion')
                driver.execute_script("arguments[0].click();", button_logout)
                time.sleep(2)

            if retry == 3 and access_to_correct_user == False:
                raise Exception("SE INGRESO A UN USUARIO INCORRECTO")

            # # Open menu 'Operación del MEM'
            # open_menu = driver.find_element(By.ID, "M5")
            # ActionChains(driver).move_to_element(open_menu).perform()
            # print("MENU 1 ABIERTO")

            open_menu = driver.find_element(By.XPATH, '//*[@id="HyperLinkMapaSitio"]')
            ActionChains(driver).move_to_element(open_menu).perform()
            print("MAPA DEL SITIO ABIERTO")

            # TODO: Des comment this part 
#             # # Open submeno 'Mercado de energía a corto plazo'
#             # open_submenu = driver.find_element(By.ID, "A11")
#             # ActionChains(driver).move_to_element(open_submenu).perform()
#             # print("MENU 2 ABIERTO")

#             # Save current tab before switch
#             original_window = driver.current_window_handle

#             link_to_resultados = driver.find_element(By.ID, "S12")
#             driver.execute_script("arguments[0].click();", link_to_resultados)
#             print("ACCEDIENDO A SECCION SOLICITADA S12")

#             # Change to required tab

#             driver.switch_to.window(driver.window_handles[-1])
#             driver.implicitly_wait(5)
#             time.sleep(10)

#             # # Download Excel file
#             # button_download_excel = driver.find_element(By.XPATH, '/html/body/div[3]/form/div[1]/div/div[2]/div[2]/div[1]/a[3]')

#         # dict_rea = create_dict_from_table()
#         dict_rea = get_info_from_table()

#         # Logout
#         driver.switch_to.window(original_window)
#         button_logout = driver.find_element(By.ID, 'btnCerrarSesion')
#         driver.execute_script("arguments[0].click();", button_logout)
#         print("Se ha cerrado sesión correctamente")
#         time.sleep(3)

#         return dict_rea
    except NoSuchElementException as e:
        print(e)
        print("------------------------------------------")
        raise Exception(f"FalloSIN: El SIM no se encuentra disponible.")
    except ElementNotInteractableException as e:
        print(e)
        print("****************************************")
        raise Exception(f"FalloSIN: El SIM no se encuentra disponible.")
    except Exception as e:
        print(e)
        raise Exception(f"FalloSIN: {e}")


def retrieve_credentials():
    credentials = get_credentials_from_pci_account()
    print("CREDENCIALES CAPTURADAS EXITOSAMENTE")
    path_folder_cert = f'/tmp/cert/{message_data["participante"]}'
    path_cer = path_folder_cert + '/cer.cer'
    path_key = path_folder_cert + '/key.key'

    os.makedirs(path_folder_cert, exist_ok=True)

    try:
        # Decode .CER and .KEY
        with open(path_cer, 'wb') as cer_file:
            decode = base64.standard_b64decode(credentials["cer"])
            cer_file.write(decode)

        with open(path_key, 'wb') as key_file:
            decode = base64.standard_b64decode(credentials["key"])
            key_file.write(decode)

        return {
            "path_cer": path_cer,
            "path_key": path_key,
            "credentials": credentials
        }
    except Exception as E:
        raise Exception("Exception retrieve credentials", E)


def handler(event=None, context=None):
    print("EVENT", event, "CONTEXT", context)
    global message_data, driver
    message_data = event
    if message_data.get('hasTest', False):
        print('INITIAL TEST DATA')
        return message_data
    print('INITIAL SCRAPER DATA')
    try:
        credentials = retrieve_credentials()
    except Exception as E:
        print(E)
        return {
            'statusCode': 500,
            'status': 'ERROR',
            'hasScrapper': True,
            'data_client': message_data,
        }
    try:
        rea = scraper(credentials)
    except Exception as E:
        print(E)
        return {
            'statusCode': 500,
            'hasScrapper': True,
            'status': 'ERROR',
            'data_client': message_data,
        }
    finally:
        try:
            driver.quit()
        except:
            print("Error al cerrar el driver")

    return {
        'statusCode': 200,
        'hasScrapper': True,
        'status': 'SUCCESS',
        'data': rea,
        'data_client': message_data
    }


if __name__ == '__main__':
    response = handler({
        "participante": "C006",
        "sistema": "BCS"
    })
    print(response)
    pass

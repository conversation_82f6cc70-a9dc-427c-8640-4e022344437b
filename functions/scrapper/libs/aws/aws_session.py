import boto3
import os

AWS_ACCESS_KEY_ID="********************"
AWS_SECRET_ACCESS_KEY="Fj/G3QUS2HeYACaWP1/evbtMb/hBq/XKKfVkux2s"
AWS_SESSION_TOKEN="IQoJb3JpZ2luX2VjEFIaCXVzLWVhc3QtMSJIMEYCIQDkfI/Uw32asKpfJY5f3CIVf9duS5dkNNAJ8e/bBgE71QIhAOYU1OY6thrDngMQryi4wZlujLlaBFN045vYoJOhBVJhKoADCEoQARoMMTc0MzgwMzAwNTUzIgyGRFSl8us2RrENalIq3QLaD5Ztw0WIzTe/sV6553mz4jg46utbXPKUWpPFSRtk3AXKYaRwerqM+PuZH+yC9oPp+o698RfJkg4azZ6TW5S5p8EN5saMpGCLRkUtDOlwjXuW0JDhVuauI21VPPVCeBWjJv5aqv8LDUfUc+6vjIVxF0Mv6kN62vRmtY3mmoERP/dJemLpdfVN5HIvj4w5PrPFB83JN0GjV4JfEKJCCZfBQamT75dESf2AzsuYKVJ3dA6JVyQU635z/Em+jPNd0kIK5i2ZmtMhwpD02mNgbrTPwoAdzfF0ht94UmHavt7etMhjG+Vhd4gX9wmsWUS7yZVMkm4wAhHZoatkbm68o3s67SzQwUZse2+I+YvW2GWEJtTOKS8zubyz3Z0wBFakml1mwAuRVfxcNDHnGKDtVsKgb6qrAzPx1Hn4XHFVQZUnqbFeeHYy9AXasroA/ZPTDI449HVGTlu0B9YXcnauMILc8MIGOqUB+hq0inZMgXFIKrqsS8n4d5263GCLDA1pEBFiqSJG9knHVvh1RuyfMwKE0jUWE5e9+YHnIa3+33WfbhP5xp+R2vGtQE8s+uu9axsY+BKES5JI2aPgp3ukjYyhY50QS8iGXtGcHL7MCyEmAAvoN9LxIq8EpiwOmMRdve6Mfgz1sMS/X0r3x9ZHS2eUxGQqwa5LFtiSLXzWsGaDpPijJDLi3pGy/Eiq"

REGION = os.getenv('REGION', 'us-east-2')


class AwsSession:
    session = boto3._get_default_session()

    def __init__(self, AWS_ACCESS_KEY_ID=AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY=AWS_SECRET_ACCESS_KEY, AWS_SESSION_TOKEN=AWS_SESSION_TOKEN):
        if AwsSession.session.get_credentials() == None:
            self.session = boto3.session.Session(
                aws_access_key_id=AWS_ACCESS_KEY_ID,
                aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
                aws_session_token=AWS_SESSION_TOKEN,
                region_name=REGION
            )

    def init_aws_service(self, service_name, type_service):
        conn_types = {
            "client": self.session.client,
            "resource": self.session.resource
        }
        conn_function = conn_types[type_service]
        service = conn_function(
            service_name=service_name, region_name=REGION)
        return service

    def cross_account_connection_service(self, role_arn, service_name, type_service):
        sts = self.init_aws_service('sts', 'client')
        print('ROLE ARN: ', role_arn)
        stsresponse = sts.assume_role(
            RoleArn=role_arn, RoleSessionName=f'CrossAccount{service_name}')
        # Save the details from assumed role into vars
        newsession_access_key = stsresponse["Credentials"]["AccessKeyId"]
        newsession_secret_access_key = stsresponse["Credentials"]["SecretAccessKey"]
        newsession_session_token = stsresponse["Credentials"]["SessionToken"]

        conn_types = {
            "client": boto3.client,
            "resource": boto3.resource
        }
        conn_function = conn_types[type_service]

        assumed_rol = conn_function(
            service_name,
            region_name='us-east-1',
            aws_access_key_id=newsession_access_key,
            aws_secret_access_key=newsession_secret_access_key,
            aws_session_token=newsession_session_token
        )

        return assumed_rol


class S3(AwsSession):
    def check_if_location_exist(self, bucket, location):
        try:
            s3 = self.init_aws_service('s3', 'client')
            exists = s3.list_objects_v2(Bucket=bucket, Prefix=location)
            if len(exists['Contents']) > 0:
                return True
            else:
                return False
        except:
            return False

    def upload_file(self, bucket, source_file_path, destination_file_path, metadata={}):
        s3 = self.init_aws_service('s3', 'client')
        s3.upload_file(source_file_path, bucket,
                       destination_file_path, ExtraArgs={'Metadata': metadata})

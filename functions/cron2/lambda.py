from datetime import datetime
import pg8000
import json
import os
import pytz
from libs.aws.aws_session import AwsSession

DB_CREDENTIALS = os.environ.get(
    'DB_CREDENTIALS',
    '{"user": "mshabes", "pass": "8Q98o05MmDJpt2su", "host": "amm-dwh.cf995cnswube.us-east-1.rds.amazonaws.com", "db": "ammdwh", "port": 5432}'
)
ARN_STEP_FUNCTION = os.environ.get(
    'ARN_STEP_FUNCTION',
    'arn:aws:states:us-east-2:174380300553:stateMachine:ReaDevStepFunction'
)

my_session = AwsSession()


def connect_db_func():
    DB_CREDENTIALS_DATA = json.loads(DB_CREDENTIALS)
    connection_db = pg8000.connect(
        host=DB_CREDENTIALS_DATA['host'],
        user=DB_CREDENTIALS_DATA['user'],
        password=DB_CREDENTIALS_DATA['pass'],
        port=DB_CREDENTIALS_DATA['port'],
        database=DB_CREDENTIALS_DATA['db']
    )
    return connection_db


def get_participants():
    def create_dict(keys, values):
        return {key: value for key, value in zip(keys, values)}

    sql = f"""
        select distinct(tcp.participante),
            tcc.sistema_cenace as sistema
        from
            ti_cat_participantes tcp
        join ti_cat_clientes tcc on
            tcc.asset_id = tcp.asset_id
        where
            tcp.participante != 'G118'
            and tcp.participante != 'M028'
            and tcp.participante != 'G079'
    """
    db_cursor.execute(sql)
    participants = db_cursor.fetchall()
    columns = [desc[0] for desc in db_cursor.description]
    participants = [create_dict(columns, row) for row in participants]
    return participants


def get_last_rea(participante, sistema_cenace):

    sql = f"select * from public.ti_rea where participante='{participante}' and sistema_cenace='{sistema_cenace}' and porcentaje is not null ORDER BY fecha DESC limit 1;"
    # print('GET LAST REA', sql)

    db_cursor.execute(sql)

    def rows_as_dicts(cursor):
        col_names = [i[0] for i in cursor.description]
        for row in cursor:
            x = dict(zip(col_names, row))
            yield x

    rows = rows_as_dicts(db_cursor)
    items = []
    for row in rows:
        items.append(row)
    return items


def execute_step_function(participants, test_items):
    step_function_client = my_session.init_aws_service(
        'stepfunctions', 'client')
    print(ARN_STEP_FUNCTION)
    step_function_client.start_execution(
        stateMachineArn=ARN_STEP_FUNCTION,
        input=json.dumps({
            "Processes": participants,
            "TestItems": test_items
        })
    )


def handler(event=None, context=None):
    global db_cursor
    db_connection = connect_db_func()
    db_connection.autocommit = True
    db_cursor = db_connection.cursor()

    participantes = []

    eval_participants = get_participants()

    for participant in eval_participants:
        result = get_last_rea(
            participant['participante'],
            participant['sistema']
        )
        if len(result) > 0:
            Tz = pytz.timezone('America/Mexico_City')
            diferencia = datetime.now(
                Tz) - result[0]['fecha'].replace(tzinfo=Tz)
            num_horas = diferencia.total_seconds() / 3600
            if num_horas >= 1:
                participantes.append(participant)

    try:
        execute_step_function(participantes, None)
    except Exception as E:
        raise Exception(json.dumps({
            'statusCode': 500,
            'body': {
                'stataus': 'ERROR',
                'description': f'Error on scraper: {E}',
                'data': "message_data"
            }
        }))
    finally:
        db_connection.close()

    return {
        'statusCode': 200,
        'data': "Procesos ejecutados exitosamente"
    }


if __name__ == '__main__':
    # handler()
    # 2h para antiguedad de nulos
    # 30min ejecuta el cron
    pass

# comment comment comment comment comment comment
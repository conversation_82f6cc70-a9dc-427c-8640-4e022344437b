import boto3
import os

AWS_ACCESS_KEY_ID = "********************"
AWS_SECRET_ACCESS_KEY = "xXgEmUYJY044jlHOmLnw7Zsjgh1knFjrHpXn7ekt"
AWS_SESSION_TOKEN = "IQoJb3JpZ2luX2VjEKD//////////wEaCXVzLWVhc3QtMSJHMEUCIEa9+ujnmfcGJuzRtum7ehrUm0UJZRkEvDVaNt/fIGlJAiEAig5Lc0wQk6TxUJPNDbKWHfxtQjp9m5GwXJuZXsffSsEqiwMImP//////////ARAAGgwzNTMyNTU2NjQ0NDkiDJQIY5nbKT/HNx16pirfAvY+dxD78qJYmqeKsFFIxdv1VThkoF+l+F/VQniw75VzWaXhhu2ku7XhlzJY6/sPsXNpthy+zFwR9vhRSEwTJ3+Vsjr4HC5fjF/l1Sdi5AoUdJxnoZrITDTgtUErpdEBN+wJ60+U6upa+rJeyU9Jmu7UPjm1m3A7gR05X/r0J20oECKdtWb2d45n4Qw5zbbdBO7LPwmOPv/9g8mG4DW1tZY4frGtV4r1+VwfioL3lzSAqKtBCcO/YI+Pc5fivEW4XzbYtjeIqGU6vMEeJPVE0yUN2cd7NmPeUl9XJLSRs8AEyx0m0UmY8F0jxuletq8rcuNyf1GBLdrBZkLpKHWsnSsHGF/g21mjMdzFMdFqKxp7UtzGexQRtM2/loP16/DwwSWXrIXhX6t8ipGBjSOG5y4KKW2t3tnWEao9gVmTl5IQtfht/lGC7syzQsLR1Q3z9sMG14GVyBwSA6sqqSZS2zDQ8dKoBjqmAZbFJ0qcQ1espQI5mRcOx6jqTykRd+aaLCwYVDs7p9Y+C8K3V97+hCHeUT9F16ggrH3d9bkCD3HLnLukQYmsf3rqeWeI72fYtzd2PQVz5C93XseEQSP1AxhVyE/QSHE0wS+j9ks5E2A1YWkTIoDoLrLyGJc6uvMt4yjgTEVo0vmAa7+dYFJI7lY10Y6E31n3and145aWCftnoC2ZTesUkdEK1lcxwd8="

REGION = os.getenv('REGION', 'us-east-2')


class AwsSession:
    session = boto3._get_default_session()

    def __init__(self, AWS_ACCESS_KEY_ID=AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY=AWS_SECRET_ACCESS_KEY, AWS_SESSION_TOKEN=AWS_SESSION_TOKEN):
        if AwsSession.session.get_credentials() == None:
            self.session = boto3.session.Session(
                aws_access_key_id=AWS_ACCESS_KEY_ID,
                aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
                aws_session_token=AWS_SESSION_TOKEN,
                region_name=REGION
            )

    def init_aws_service(self, service_name, type_service):
        conn_types = {
            "client": self.session.client,
            "resource": self.session.resource
        }
        conn_function = conn_types[type_service]
        service = conn_function(
            service_name=service_name, region_name=REGION)
        return service

    def cross_account_connection_service(self, role_arn, service_name, type_service):
        sts = self.init_aws_service('sts', 'client')
        print('ROLE ARN: ', role_arn)
        stsresponse = sts.assume_role(
            RoleArn=role_arn, RoleSessionName=f'CrossAccount{service_name}')
        # Save the details from assumed role into vars
        newsession_access_key = stsresponse["Credentials"]["AccessKeyId"]
        newsession_secret_access_key = stsresponse["Credentials"]["SecretAccessKey"]
        newsession_session_token = stsresponse["Credentials"]["SessionToken"]

        conn_types = {
            "client": boto3.client,
            "resource": boto3.resource
        }
        conn_function = conn_types[type_service]

        assumed_rol = conn_function(
            service_name,
            region_name='us-east-1',
            aws_access_key_id=newsession_access_key,
            aws_secret_access_key=newsession_secret_access_key,
            aws_session_token=newsession_session_token
        )

        return assumed_rol


class S3(AwsSession):
    def check_if_location_exist(self, bucket, location):
        try:
            s3 = self.init_aws_service('s3', 'client')
            exists = s3.list_objects_v2(Bucket=bucket, Prefix=location)
            if len(exists['Contents']) > 0:
                return True
            else:
                return False
        except:
            return False

    def upload_file(self, bucket, source_file_path, destination_file_path, metadata={}):
        s3 = self.init_aws_service('s3', 'client')
        s3.upload_file(source_file_path, bucket,
                       destination_file_path, ExtraArgs={'Metadata': metadata})

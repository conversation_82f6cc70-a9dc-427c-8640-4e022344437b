Resources:
  BucketRoledev:
    Type: AWS::IAM::Role
    Properties:
      RoleName: ps-op-proceso-rea-dev-Role
      MaxSessionDuration: 3600
      AssumeRolePolicyDocument:
        Statement:
        - Effect: Allow
          Principal:
            Service: 
              - lambda.amazonaws.com
              - states.amazonaws.com
          Action:
          - sts:AssumeRole
      ManagedPolicyArns:
      - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      - arn:aws:iam::************:policy/CrossAccountSESSendEmail
      - arn:aws:iam::aws:policy/AmazonSSMFullAccess
      - arn:aws:iam::aws:policy/AmazonSESFullAccess
      - Ref: AccessBucketPolicydev
      - Ref: StepFunctionPolicydev
      - Ref: FullAccessSQSPolicydev
      - Ref: SecretsManagerPolicydev
      - Ref: DataLakeAccessCrossAccountPolicydev
  
  BucketRoleprd:
    Type: AWS::IAM::Role
    Properties:
      RoleName: ps-op-proceso-rea-prd-Role
      MaxSessionDuration: 3600
      AssumeRolePolicyDocument:
        Statement:
        - Effect: Allow
          Principal:
            Service: 
              - lambda.amazonaws.com
              - states.amazonaws.com
          Action:
          - sts:AssumeRole
      ManagedPolicyArns:
      - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      - arn:aws:iam::************:policy/CrossAccountSESSendEmail
      - arn:aws:iam::aws:policy/AmazonSSMFullAccess
      - arn:aws:iam::aws:policy/AmazonSESFullAccess
      - Ref: AccessBucketPolicyprd
      - Ref: StepFunctionPolicyprd
      - Ref: FullAccessSQSPolicyprd
      - Ref: SecretsManagerPolicyprd
      - Ref: DataLakeAccessCrossAccountPolicyprd
service: ps-op-proceso-rea-iam

provider:
  name: aws
  versionFunctions: false
  runtime: python3.11
  stage: ${self:custom.stage}
  region: ${self:custom.region}
  deploymentBucket:
    name: serverless-deployment-************-global-bucket-${self:custom.stage}
    serverSideEncryption: AES256

  tags:
    author: caguilar
    env: ${self:custom.stage}
    project-name: proceso-rea

custom:
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  projectName: ${opt:projectName, 'Rea'}
  config:
    dev:
      account_id: '************'
      name_stage: 'dev'
      name_bucket: 'ammper-rea-${self:custom.stage}'
    prod:
      account_id: '************'
      name_stage: 'prd'
      name_bucket: 'ammper-rea-${self:custom.stage}'

resources:
- ${file(roles.yml)}
- ${file(policies.yml)}
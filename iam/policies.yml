Resources:
  AccessBucketPolicydev:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: ps-op-proceso-rea-dev-PolicyS3
      Description: Lambda policy to allow access to S3 bucket in dev stage
      Path: '/'
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
        - Effect: Allow
          Action:
          - s3:*
          - s3-object-lambda:*
          Resource:
          - arn:aws:s3:::amm-clients-documents-work
          - arn:aws:s3:::amm-clients-documents-work/*
          - arn:aws:s3:::ammper-rea-dev
          - arn:aws:s3:::ammper-rea-dev/*

  StepFunctionPolicydev:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: ps-op-proceso-rea-dev-PolicyStepFunction
      Description: Lambda policy to allow executa step functions in dev stage
      Path: '/'
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
        - Effect: Allow
          Action:
          - lambda:InvokeFunction
          - states:*
          Resource: '*'

  FullAccessSQSPolicydev:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: ps-op-proceso-rea-dev-PolicySQS
      Description: Lambda policy to allow access to SQS in dev stage
      Path: "/"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
        - Effect: Allow
          Action:
          - sqs:*
          Resource: '*'

  SecretsManagerPolicydev:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: ps-op-proceso-rea-dev-PolicySecretsManager
      Description: Lambda policy to allow access to Secrets Manager in dev stage
      Path: "/"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
        - Effect: Allow
          Action:
          - secretsmanager:GetSecretValue
          - secretsmanager:*
          - kms:*
          Resource:
          - arn:aws:secretsmanager:*
          - arn:aws:kms:*

  DataLakeAccessCrossAccountPolicydev:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: ps-op-proceso-rea-dev-DataLakeCrossAccountPolicy
      Description: Lambda policy to allow access to SSM Parameter Store from another account in ${self:custom.stage} stage
      Path: '/'
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
        - Effect: Allow
          Action:
          - sts:*
          Resource:
          - arn:aws:iam::************:role/CrossAccountSecretsManagerPCI

  EmailSesPolicydev:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: ps-op-proceso-rea-dev-EmailSesPolicy
      Description: Lambda policy to allow access to SES emails in dev stage
      Path: '/'
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
        - Effect: Allow
          Action:
          - ses:SendEmail
          - ses:SendRawEmail
          Resource: '*'


  AccessBucketPolicyprd:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: ps-op-proceso-rea-prd-PolicyS3
      Description: Lambda policy to allow access to S3 bucket in prd stage
      Path: '/'
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
        - Effect: Allow
          Action:
          - s3:*
          - s3-object-lambda:*
          Resource:
          - arn:aws:s3:::amm-clients-documents-work
          - arn:aws:s3:::amm-clients-documents-work/*
          - arn:aws:s3:::ammper-rea-prod
          - arn:aws:s3:::ammper-rea-prod/*

  StepFunctionPolicyprd:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: ps-op-proceso-rea-prd-PolicyStepFunction
      Description: Lambda policy to allow executa step functions in prd stage
      Path: '/'
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
        - Effect: Allow
          Action:
          - lambda:InvokeFunction
          - states:*
          Resource: '*'

  FullAccessSQSPolicyprd:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: ps-op-proceso-rea-prd-PolicySQS
      Description: Lambda policy to allow access to SQS in dev stage
      Path: "/"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
        - Effect: Allow
          Action:
          - sqs:*
          Resource: '*'

  SecretsManagerPolicyprd:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: ps-op-proceso-rea-prd-PolicySecretsManager
      Description: Lambda policy to allow access to Secrets Manager in prd stage
      Path: "/"
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
        - Effect: Allow
          Action:
          - secretsmanager:GetSecretValue
          - secretsmanager:*
          - kms:*
          Resource:
          - arn:aws:secretsmanager:*
          - arn:aws:kms:*

  DataLakeAccessCrossAccountPolicyprd:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: ps-op-proceso-rea-prd-DataLakeCrossAccountPolicy
      Description: Lambda policy to allow access to SSM Parameter Store from another account in prd stage
      Path: '/'
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
        - Effect: Allow
          Action:
          - sts:*
          Resource:
          - arn:aws:iam::************:role/CrossAccountSecretsManagerPCI

  EmailSesPolicyprd:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: ps-op-proceso-rea-prd-EmailSesPolicy
      Description: Lambda policy to allow access to SES emails in prd stage
      Path: '/'
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
        - Effect: Allow
          Action:
          - ses:SendEmail
          - ses:SendRawEmail
          Resource: '*'
